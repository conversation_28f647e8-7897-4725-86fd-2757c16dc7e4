"use client";

import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Sidebar from "./Sidebar";
import TopBar from "./TopBar";

// Map routes to sidebar item IDs
const routeToItemId = {
  "/dashboard": 0,
  "/sales-invoice-generator": 1,
  "/invoice-automation": 2,
  "/accounting-tax-assistant": 3,
  "/xero": 4,
  "/profile": 0 // Profile uses dashboard as active item
};

// Function to get the active item ID from pathname
const getActiveItemId = (pathname) => {
  // Direct match
  if (routeToItemId[pathname] !== undefined) {
    return routeToItemId[pathname];
  }

  // Check if it's a sub-route
  if (pathname.startsWith('/xero/')) {
    return routeToItemId['/xero'];
  }

  // Default to dashboard
  return 0;
};

// Map sidebar item IDs to routes
const itemIdToRoute = {
  0: "/dashboard",
  1: "/sales-invoice-generator",
  2: "/invoice-automation",
  3: "/accounting-tax-assistant",
  4: "/xero"
};

export default function AppLayout({ children }) {
  const pathname = usePathname();
  const router = useRouter();
  const [activeItem, setActiveItem] = useState(getActiveItemId(pathname));
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Handle sidebar item selection
  const handleItemSelect = (itemId) => {
    setActiveItem(itemId);
    router.push(itemIdToRoute[itemId]);
  };

  // Update active item when route changes
  useEffect(() => {
    setActiveItem(getActiveItemId(pathname));
  }, [pathname]);

  // Toggle sidebar collapse
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="flex flex-col min-h-screen">
      <div className="flex flex-1 p-2 gap-2">
        {/* Sidebar */}
        <div className="flex-none">
          <Sidebar
            activeItem={activeItem}
            setActiveItem={handleItemSelect}
            collapsed={sidebarCollapsed}
            onToggle={toggleSidebar}
          />
        </div>

        <div className="flex flex-col flex-1 space-y-2">
          {/* TopBar */}
          <TopBar
            sidebarCollapsed={sidebarCollapsed}
            onToggleSidebar={toggleSidebar}
          />

          {/* Content Area */}
          <div className="flex-1 bg-white rounded-xl shadow-lg p-6 w-full">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}