"use client";

import { motion } from "framer-motion";
import React, { useEffect, useState } from "react";
import EmailSending from "./components/EmailSending";
import EmailTemplate from "./components/EmailTemplate";
import FileDataMap from "./components/FileDataMap";
import InvoicePreview from "./components/InvoicePreview";
import OptionSelection from "./components/OptionSelection";
import PdfUpload from "./components/PdfUpload";
import SavedTemplateSelection from "./components/SavedTemplateSelection";
import TemplatePreview from "./components/TemplatePreview";
import MizuTemplateSelection from "./components/MizuTemplateSelection";
import CompanyDetailsForm from "./components/CompanyDetailsForm";
import MizuTemplatePreview from "./components/MizuTemplatePreview";

export default function SalesInvoiceGenerator() {
  // State for navigation and data
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedOption, setSelectedOption] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [extractedData, setExtractedData] = useState(null);
  const [customLogo, setCustomLogo] = useState(null);
  const [columnMappings, setColumnMappings] = useState(null);
  const [invoiceData, setInvoiceData] = useState(null);
  const [emailTemplate, setEmailTemplate] = useState(null);
  const [emailsSent, setEmailsSent] = useState(false);
  const [history, setHistory] = useState([0]);
  const [stateLoaded, setStateLoaded] = useState(false);
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // New state for templates
  const [inTemplateSelection, setInTemplateSelection] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [savedTemplates, setSavedTemplates] = useState([]);
  const [usingSavedTemplate, setUsingSavedTemplate] = useState(false);
  const [templateName, setTemplateName] = useState("");
  const [nameError, setNameError] = useState("");

  // New state for MizuFlow templates
  const [inMizuTemplateSelection, setInMizuTemplateSelection] = useState(false);
  const [selectedMizuTemplate, setSelectedMizuTemplate] = useState(null);
  const [companyData, setCompanyData] = useState(null);
  const [inCompanyForm, setInCompanyForm] = useState(false);
  const [inMizuPreview, setInMizuPreview] = useState(false);

  // State for reset confirmation
  const [showResetConfirmation, setShowResetConfirmation] = useState(false);

  // Animation variants for smooth transitions
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  };

  // Breadcrumb step labels
  const stepLabels = [
    "Select Option",
    "Upload Sample PDF",
    "Extract Template",
    "Upload & Map Data",
    "Generate Invoice",
    "Setup Email Template",
    "Send Emails"
  ];

  // Load saved templates from localStorage when component mounts
  useEffect(() => {
    try {
      const templatesData = localStorage.getItem('savedInvoiceTemplates');
      if (templatesData) {
        setSavedTemplates(JSON.parse(templatesData));
      }
    } catch (error) {
      console.error('Error loading saved templates:', error);
    }
  }, []);

  // Load state from localStorage when component mounts
  useEffect(() => {
    try {
      const savedState = localStorage.getItem('invoiceGeneratorState');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        setCurrentStep(parsedState.currentStep || 0);
        setSelectedOption(parsedState.selectedOption || null);
        setExtractedData(parsedState.extractedData || null);
        setCustomLogo(parsedState.customLogo || null);
        setColumnMappings(parsedState.columnMappings || null);
        setInvoiceData(parsedState.invoiceData || null);
        setEmailTemplate(parsedState.emailTemplate || null);
        setEmailsSent(parsedState.emailsSent || false);
        setHistory(parsedState.history || [0]);
        setUsingSavedTemplate(parsedState.usingSavedTemplate || false);
      }
    } catch (error) {
      console.error('Error loading state from localStorage:', error);
    }
    setStateLoaded(true);
  }, []);

  // Save state to localStorage whenever state changes
  useEffect(() => {
    if (!stateLoaded) return; // Skip initial render

    try {
      const stateToSave = {
        currentStep,
        selectedOption,
        extractedData,
        customLogo,
        columnMappings,
        invoiceData,
        emailTemplate,
        emailsSent,
        history,
        usingSavedTemplate
      };

      localStorage.setItem('invoiceGeneratorState', JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Error saving state to localStorage:', error);
    }
  }, [
    stateLoaded,
    currentStep,
    selectedOption,
    extractedData,
    customLogo,
    columnMappings,
    invoiceData,
    emailTemplate,
    emailsSent,
    history,
    usingSavedTemplate
  ]);

  // Handle navigation
  const goToStep = (step) => {
    // If using saved template and trying to go to steps 1 or 2, prevent it
    if (usingSavedTemplate && (step === 1 || step === 2)) {
      // If trying to go back from step 3, go to step 0 (option selection)
      if (currentStep === 3) {
        step = 0;
        setUsingSavedTemplate(false); // Reset the saved template flag
      } else {
        return; // Don't allow navigation to steps 1 and 2 when using saved template
      }
    }

    if (step < currentStep) {
      // Going back - update history
      setHistory(history.slice(0, history.indexOf(step) + 1));
    } else if (step > currentStep) {
      // Going forward - add to history
      setHistory([...history, step]);
    }
    setCurrentStep(step);
  };

  // Reset all state - used when going back to home from email sending
  const resetState = () => {
    setCurrentStep(0);
    setSelectedOption(null);
    setUploadedFile(null);
    setExtractedData(null);
    setCustomLogo(null);
    setColumnMappings(null);
    setInvoiceData(null);
    setEmailTemplate(null);
    setEmailsSent(false);
    setHistory([0]);
    setInTemplateSelection(false);
    setSelectedTemplate(null);
    setUsingSavedTemplate(false);
    setTemplateName("");
    setNameError("");

    // Reset MizuFlow state
    setInMizuTemplateSelection(false);
    setSelectedMizuTemplate(null);
    setCompanyData(null);
    setInCompanyForm(false);
    setInMizuPreview(false);

    // Clear localStorage
    localStorage.removeItem('invoiceGeneratorState');

    // Hide reset confirmation dialog
    setShowResetConfirmation(false);
  };

  // Handle initiating reset
  const handleResetClick = () => {
    setShowResetConfirmation(true);
  };

  // Handle cancel reset
  const handleCancelReset = () => {
    setShowResetConfirmation(false);
  };

  // Dummy API function to process PDF
  const processPdf = async (file) => {
    // Simulate API call with a delay
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          fields: {
            invoiceNumber: "INV-001",
            date: "2023-08-15",
            dueDate: "2023-09-15",
            customerName: "Acme Corporation",
            customerEmail: "<EMAIL>",
            items: [
              { description: "Product A", quantity: 2, price: 100 },
              { description: "Service B", quantity: 1, price: 200 },
            ],
            subtotal: 400,
            tax: 40,
            total: 440
          },
          invoiceImage: "https://placehold.co/600x800/e2e8f0/1e293b?text=Invoice+Preview",
          logoImage: "https://placehold.co/200x100/e2e8f0/1e293b?text=Company+Logo"
        });
      }, 1500);
    });
  };

  // Handle file upload and processing
  const handleFileUpload = async (file) => {
    setUploadedFile(file);
    const data = await processPdf(file);
    setExtractedData(data);
    goToStep(2); // Move to template preview step
  };

  // Handle option selection
  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    if (option === "createNew") {
      goToStep(1); // Move to PDF upload step
    } else if (option === "useSaved") {
      setInTemplateSelection(true); // Show template selection screen
    } else if (option === "useMizu") {
      setInMizuTemplateSelection(true); // Show MizuFlow template selection
    }
  };

  // Handle back from template selection
  const handleTemplateSelectionBack = () => {
    setInTemplateSelection(false);
    setSelectedOption(null);
  };

  // Handle template selection
  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
    setExtractedData(template.extractedData);
    setCustomLogo(template.customLogo);
    setUsingSavedTemplate(true);

    // Update the template's lastUsed date
    const updatedTemplates = savedTemplates.map(t =>
      t.id === template.id
        ? {...t, lastUsed: new Date().toISOString().split('T')[0]}
        : t
    );

    setSavedTemplates(updatedTemplates);
    localStorage.setItem('savedInvoiceTemplates', JSON.stringify(updatedTemplates));

    // Skip to data mapping step
    goToStep(3);
    setInTemplateSelection(false);
  };

  // Handle MizuFlow template selection
  const handleMizuTemplateSelect = (template) => {
    setSelectedMizuTemplate(template);
    setInMizuTemplateSelection(false);
    setInCompanyForm(true);
  };

  // Handle back from MizuFlow template selection
  const handleMizuTemplateSelectionBack = () => {
    setInMizuTemplateSelection(false);
    setSelectedOption(null);
  };

  // Handle company form completion
  const handleCompanyFormNext = (formData) => {
    setCompanyData(formData);
    setInCompanyForm(false);
    setInMizuPreview(true);
  };

  // Handle back from company form
  const handleCompanyFormBack = () => {
    setInCompanyForm(false);
    setInMizuTemplateSelection(true);
  };

  // Handle MizuFlow template preview proceed
  const handleMizuPreviewProceed = (data) => {
    // Convert MizuFlow data to the format expected by the rest of the app
    const extractedData = {
      invoiceImage: null, // No image for MizuFlow templates
      logoImage: data.companyData.logo ? URL.createObjectURL(data.companyData.logo) : null,
      fields: [
        { id: 'company_name', name: 'Company Name', value: data.companyData.company_name },
        { id: 'email', name: 'Email', value: data.companyData.email },
        { id: 'phone', name: 'Phone', value: data.companyData.phone },
        { id: 'address', name: 'Address', value: data.companyData.address_line_1 },
        { id: 'invoice_number', name: 'Invoice Number', value: data.staticData.invoice.number },
        { id: 'invoice_date', name: 'Invoice Date', value: data.staticData.invoice.date },
        { id: 'client_name', name: 'Client Name', value: data.staticData.client.name },
        { id: 'total', name: 'Total Amount', value: data.staticData.invoice.total.toString() }
      ]
    };

    setExtractedData(extractedData);
    setCustomLogo(data.companyData.logo ? URL.createObjectURL(data.companyData.logo) : null);
    setInMizuPreview(false);
    goToStep(3); // Move to data mapping step
  };

  // Handle back from MizuFlow template preview
  const handleMizuPreviewBack = () => {
    setInMizuPreview(false);
    setInCompanyForm(true);
  };

  // Handle proceeding to data mapping
  const handleProceedToDataMapping = () => {
    goToStep(3); // Move to data mapping step
  };

  // Handle mapping completion and invoice generation
  const handleMappingComplete = (data) => {
    setInvoiceData(data);
    goToStep(4); // Move to invoice preview step
  };

  // Handle proceeding to email template
  const handleProceedToEmailTemplate = () => {
    goToStep(5); // Move to email template step
  };

  // Handle email template completion
  const handleEmailTemplateComplete = (template) => {
    setEmailTemplate(template);
    goToStep(6); // Move to email sending step
  };

  // Handle emails sent
  const handleEmailsSent = (goToHome = false) => {
    setEmailsSent(true);

    // If goToHome is true, reset state and go to initial page
    if (goToHome) {
      resetState();
    }
  };

  // Handle saving template functionality
  const handleSaveTemplate = () => {
    if (!extractedData) return;

    // Validate template name
    if (!templateName.trim()) {
      setNameError("Please enter a template name");
      return;
    }

    // Check for duplicate names
    if (savedTemplates.some(template => template.name.toLowerCase() === templateName.trim().toLowerCase())) {
      setNameError("A template with this name already exists");
      return;
    }

    // Create a template object with a unique ID
    const templateId = `template_${Date.now()}`;
    const newTemplate = {
      id: templateId,
      name: templateName.trim(),
      extractedData,
      customLogo,
      createdAt: new Date().toISOString().split('T')[0],
      lastUsed: null
    };

    // Add to savedTemplates
    const updatedTemplates = [...savedTemplates, newTemplate];
    setSavedTemplates(updatedTemplates);

    // Save to localStorage
    localStorage.setItem('savedInvoiceTemplates', JSON.stringify(updatedTemplates));

    // Reset name and error
    setTemplateName("");
    setNameError("");

    setShowSaveConfirmation(false);
    setShowSuccessMessage(true);
  };

  // Handle finish button click
  const handleFinish = () => {
    setTemplateName("");
    setNameError("");
    setShowSaveConfirmation(true);
  };

  // Handle confirmation dialog "No" button
  const handleNoSave = () => {
    setTemplateName("");
    setNameError("");
    setShowSaveConfirmation(false);
    resetState();
  };

  // Handle success message "OK" button
  const handleSuccessOk = () => {
    setShowSuccessMessage(false);
    resetState();
  };

  // Navigation buttons configuration
  const canGoBack = () => {
    if (inTemplateSelection) return false;
    if (inMizuTemplateSelection) return false;
    if (inCompanyForm) return false;
    if (inMizuPreview) return false;

    // If at step 3 and using saved template, we need custom back behavior to step 0
    if (usingSavedTemplate && currentStep === 3) {
      return true; // Always allow going back from step 3 to option selection
    }

    // Otherwise, standard logic - enabled for all steps except the first
    return currentStep > 0;
  };

  // Determine if Next button should be enabled
  const canGoForward = () => {
    // If we're in template selection or MizuFlow states, don't show forward button
    if (inTemplateSelection) return false;
    if (inMizuTemplateSelection) return false;
    if (inCompanyForm) return false;
    if (inMizuPreview) return false;

    // If we have this step in history already (already visited), enable the button
    if (history.includes(currentStep + 1)) return true;

    // Logic for when Next should be enabled based on current step
    switch (currentStep) {
      case 0:
        return selectedOption !== null; // Enable if an option is selected
      case 1:
        return uploadedFile !== null; // Enable if a file is uploaded
      case 2:
        // For Template Preview, check if user has ever proceeded to step 3 or beyond
        return history.some(step => step > 2) || extractedData !== null;
      case 3:
        return invoiceData !== null; // Enable if invoices are generated
      case 4:
        return false; // No next button on invoice preview - use Send Emails instead
      case 5:
        return emailTemplate !== null; // Enable if email template is set
      case 6:
        return true; // Enable Finish button on email sending
      default:
        return false;
    }
  };

  // Render the appropriate step component
  const renderStep = () => {
    // If in template selection mode, show that instead
    if (inTemplateSelection) {
      return <SavedTemplateSelection
               onSelect={handleTemplateSelect}
               onBack={handleTemplateSelectionBack}
               variants={itemVariants}
             />;
    }

    // If in MizuFlow template selection mode
    if (inMizuTemplateSelection) {
      return <MizuTemplateSelection
               onSelect={handleMizuTemplateSelect}
               onBack={handleMizuTemplateSelectionBack}
               variants={itemVariants}
             />;
    }

    // If in company form mode
    if (inCompanyForm) {
      return <CompanyDetailsForm
               onNext={handleCompanyFormNext}
               onBack={handleCompanyFormBack}
               selectedTemplate={selectedMizuTemplate}
               variants={itemVariants}
             />;
    }

    // If in MizuFlow template preview mode
    if (inMizuPreview) {
      return <MizuTemplatePreview
               selectedTemplate={selectedMizuTemplate}
               companyData={companyData}
               onBack={handleMizuPreviewBack}
               onProceed={handleMizuPreviewProceed}
               variants={itemVariants}
             />;
    }

    switch (currentStep) {
      case 0:
        return <OptionSelection onSelect={handleOptionSelect} variants={itemVariants} />;
      case 1:
        return <PdfUpload onUpload={handleFileUpload} variants={itemVariants} />;
      case 2:
        return <TemplatePreview
                 data={extractedData}
                 customLogo={customLogo}
                 onLogoChange={setCustomLogo}
                 onProceed={handleProceedToDataMapping}
                 variants={itemVariants}
               />;
      case 3:
        return <FileDataMap
                 variants={itemVariants}
                 onComplete={handleMappingComplete}
               />;
      case 4:
        return <InvoicePreview
                 variants={itemVariants}
                 invoiceData={invoiceData}
                 onSendEmails={handleProceedToEmailTemplate}
               />;
      case 5:
        return <EmailTemplate
                 variants={itemVariants}
                 invoiceData={invoiceData}
                 onNext={handleEmailTemplateComplete}
               />;
      case 6:
        return <EmailSending
                 variants={itemVariants}
                 invoiceData={invoiceData}
                 emailTemplate={emailTemplate}
                 onComplete={handleEmailsSent}
               />;
      default:
        return <OptionSelection onSelect={handleOptionSelect} variants={itemVariants} />;
    }
  };

  // Total number of steps
  const totalSteps = 7;

  return (
    <div className="w-full min-h-screen bg-gray-50 p-6 flex flex-col">
      {/* Breadcrumb Navigation */}
      {currentStep > 0 && !inTemplateSelection && !inMizuTemplateSelection && !inCompanyForm && !inMizuPreview && (
        <div className="w-full max-w-7xl mx-auto mb-6">
          <div className="flex items-center justify-between">
            <motion.button
              className={`w-12 h-12 rounded-full flex items-center justify-center ${canGoBack()
                ? 'bg-blue-600 text-white shadow-md hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
              disabled={!canGoBack()}
              onClick={() => canGoBack() && goToStep(currentStep - 1)}
              whileHover={canGoBack() ? { scale: 1.05 } : {}}
              whileTap={canGoBack() ? { scale: 0.95 } : {}}
              aria-label="Previous step"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </motion.button>

            <div className="hidden md:flex items-center justify-center flex-1 px-8">
              <div className="w-full flex items-center justify-center">
                {/* Create visible steps array first */}
                {(() => {
                  // Create an array of visible steps
                  const visibleSteps = stepLabels.slice(1).map((label, index) => {
                    const adjustedIndex = index + 1;
                    // Skip steps 1 and 2 when using a saved template
                    if (usingSavedTemplate && (adjustedIndex === 1 || adjustedIndex === 2)) {
                      return null;
                    }

                    // For a saved template, adjust the displayed step number
                    let displayedStep = adjustedIndex;
                    if (usingSavedTemplate && adjustedIndex > 2) {
                      displayedStep = adjustedIndex - 2;
                    }

                    return {
                      label,
                      adjustedIndex,
                      displayedStep
                    };
                  }).filter(Boolean); // Filter out null items

                  // Now render items with connectors between them
                  return visibleSteps.map((step, idx) => {
                    const { label, adjustedIndex, displayedStep } = step;
                    const isActive = adjustedIndex === currentStep;
                    const isCompleted = adjustedIndex < currentStep;
                    const isAccessible = adjustedIndex <= Math.max(...history);

                    return (
                      <React.Fragment key={adjustedIndex}>
                        {/* Add connector before items (except first) */}
                        {idx > 0 && (
                          <div
                            className="w-24 h-[2px] mx-2"
                            style={{
                              backgroundColor: visibleSteps[idx-1].adjustedIndex < currentStep
                                ? '#22c55e' : '#e5e7eb'
                            }}
                          />
                        )}

                        <div className="flex flex-col items-center relative">
                          {/* Step Circle */}
                          <button
                            className={`group ${
                              isAccessible ? 'cursor-pointer' : 'cursor-not-allowed'
                            }`}
                            onClick={() => isAccessible && goToStep(adjustedIndex)}
                            disabled={!isAccessible}
                          >
                            {/* Circle */}
                            <div
                              className={`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out
                                ${isCompleted
                                    ? 'bg-gradient-to-br from-green-400 to-green-600 shadow-md'
                                    : isActive
                                        ? 'bg-gradient-to-br from-blue-400 to-blue-600 shadow-lg'
                                        : 'bg-gray-200 group-hover:bg-gray-300'
                                }`}
                            >
                              {isCompleted ? (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              ) : (
                                <span className={`text-sm font-semibold ${isActive ? 'text-white' : 'text-gray-600'}`}>
                                  {usingSavedTemplate ? displayedStep : (idx + 1)}
                                </span>
                              )}
                            </div>
                          </button>

                          {/* Label - only shown on larger screens */}
                          <span className={`mt-2 text-xs font-medium text-center hidden lg:block ${
                            isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                          }`}>
                            {label}
                          </span>
                        </div>
                      </React.Fragment>
                    );
                  });
                })()}
              </div>
            </div>

            <div className="md:hidden text-gray-700 font-medium">
              {currentStep > 0 ? `Step ${usingSavedTemplate && currentStep > 2 ? currentStep - 2 : currentStep} of ${usingSavedTemplate ? totalSteps - 3 : totalSteps - 1}` : ''}
            </div>

            <div className="flex items-center space-x-3">
              {currentStep < 6 ? (
                /* Next Button */
                <motion.button
                  className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    canGoForward()
                      ? 'bg-blue-600 text-white shadow-md hover:bg-blue-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                  disabled={!canGoForward()}
                  onClick={() => canGoForward() && goToStep(currentStep + 1)}
                  whileHover={canGoForward() ? { scale: 1.05 } : {}}
                  whileTap={canGoForward() ? { scale: 0.95 } : {}}
                  aria-label="Next step"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </motion.button>
              ) : (
                /* Finish Button */
                <motion.button
                  className={`px-6 py-2 rounded-lg ${
                    canGoForward()
                      ? 'bg-green-600 text-white font-medium shadow-md hover:bg-green-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                  disabled={!canGoForward()}
                  onClick={() => canGoForward() && handleFinish()}
                  whileHover={canGoForward() ? { scale: 1.05 } : {}}
                  whileTap={canGoForward() ? { scale: 0.95 } : {}}
                >
                  Finish
                </motion.button>
              )}

              {/* Reset Button - now positioned after the next/finish button */}
              {(currentStep > 0 || inTemplateSelection) && (
                <motion.button
                  onClick={handleResetClick}
                  className="w-12 h-12 rounded-full flex items-center justify-center bg-red-100 text-red-600 hover:bg-red-200 shadow-md"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  aria-label="Reset process"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </motion.button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <motion.div
        className="bg-white rounded-xl shadow-lg p-4 max-w-full w-full"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.h1
          className="text-2xl font-bold text-center mb-4 text-gray-800"
          variants={itemVariants}
        >
          Invoice Generator
        </motion.h1>

        {/* Reset Button for Template Selection */}
        {inTemplateSelection && (
          <div className="absolute top-4 right-4">
            <motion.button
              onClick={handleResetClick}
              className="w-12 h-12 rounded-full flex items-center justify-center bg-red-100 text-red-600 hover:bg-red-200 shadow-md"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              aria-label="Reset process"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </motion.button>
          </div>
        )}

        {renderStep()}

        {/* Save Template Confirmation Dialog */}
        {showSaveConfirmation && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 max-w-md w-full shadow-xl">
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Save Template</h3>
              <p className="text-gray-600 mb-4">Would you like to save this template for future use?</p>

              <div className="mb-5">
                <label htmlFor="templateName" className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name
                </label>
                <input
                  type="text"
                  id="templateName"
                  className={`w-full px-3 py-2 border ${nameError ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 bg-white`}
                  placeholder="Enter a name for your template"
                  value={templateName}
                  onChange={(e) => {
                    setTemplateName(e.target.value);
                    if (e.target.value.trim()) setNameError("");
                  }}
                  autoFocus
                />
                {nameError && (
                  <p className="mt-1 text-sm text-red-600">{nameError}</p>
                )}
              </div>

              <div className="flex justify-end gap-3">
                <button
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                  onClick={handleNoSave}
                >
                  No
                </button>
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  onClick={handleSaveTemplate}
                >
                  Save Template
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 max-w-md w-full shadow-xl">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2 text-center">Template Saved!</h3>
              <p className="text-gray-600 mb-6 text-center">
                Your template &quot;{templateName}&quot; has been saved successfully.
              </p>
              <div className="flex justify-center">
                <button
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  onClick={handleSuccessOk}
                >
                  OK
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Reset Confirmation Dialog */}
        {showResetConfirmation && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 max-w-md w-full shadow-xl">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-800">Reset Process?</h3>
              </div>
              <p className="text-gray-600 mb-6">
                This will reset all your progress and return to the main page. Any unsaved work will be lost.
              </p>
              <div className="flex justify-end gap-3">
                <button
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                  onClick={handleCancelReset}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  onClick={resetState}
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
}